const http = require('http');
const net = require('net');

/**
 * 检查端口是否被占用
 * @param {number} port 端口号
 * @returns {Promise<boolean>} 端口是否可用
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * 获取随机可用端口
 * @param {number} startPort 起始端口
 * @param {number} endPort 结束端口
 * @returns {Promise<number>} 可用端口号
 */
async function getAvailablePort(startPort = 3000, endPort = 9999) {
    for (let port = startPort; port <= endPort; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    
    // 如果指定范围内没有可用端口，尝试系统分配
    return new Promise((resolve, reject) => {
        const server = net.createServer();
        server.listen(0, () => {
            const port = server.address().port;
            server.close(() => resolve(port));
        });
        server.on('error', reject);
    });
}

/**
 * 创建简单的HTTP服务器
 * @param {number} port 端口号
 * @returns {Promise<http.Server>} HTTP服务器实例
 */
function createHttpServer(port) {
    return new Promise((resolve, reject) => {
        const server = http.createServer((req, res) => {
            // 设置CORS头
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            
            // 简单的路由处理
            if (req.url === '/') {
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>VSCode Local HTTP Server</title>
                        <meta charset="utf-8">
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; }
                            .container { max-width: 600px; margin: 0 auto; }
                            .status { color: #28a745; font-weight: bold; }
                            .info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🚀 VSCode Local HTTP Server</h1>
                            <p class="status">✅ 服务器运行中</p>
                            <div class="info">
                                <h3>服务器信息</h3>
                                <p><strong>端口:</strong> ${port}</p>
                                <p><strong>地址:</strong> http://localhost:${port}</p>
                                <p><strong>启动时间:</strong> ${new Date().toLocaleString()}</p>
                            </div>
                            <div class="info">
                                <h3>可用接口</h3>
                                <ul>
                                    <li><a href="/api/status">GET /api/status</a> - 服务器状态</li>
                                    <li><a href="/api/time">GET /api/time</a> - 当前时间</li>
                                    <li>POST /api/echo - 回显请求数据</li>
                                </ul>
                            </div>
                        </div>
                    </body>
                    </html>
                `);
            } else if (req.url === '/api/status') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    status: 'running',
                    port: port,
                    uptime: process.uptime(),
                    timestamp: new Date().toISOString()
                }));
            } else if (req.url === '/api/time') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    time: new Date().toISOString(),
                    timestamp: Date.now()
                }));
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    error: 'Not Found',
                    message: `路径 ${req.url} 不存在`,
                    timestamp: new Date().toISOString()
                }));
            }
        });
        
        server.listen(port, () => {
            console.log(`HTTP服务器启动成功，端口: ${port}`);
            resolve(server);
        });
        
        server.on('error', (err) => {
            console.error('HTTP服务器启动失败:', err);
            reject(err);
        });
    });
}

// 测试函数
async function testHttpServer() {
    try {
        console.log('🔍 正在查找可用端口...');
        const port = await getAvailablePort();
        console.log(`✅ 找到可用端口: ${port}`);
        
        console.log('🚀 启动HTTP服务器...');
        const server = await createHttpServer(port);
        
        console.log(`🌐 服务器已启动: http://localhost:${port}`);
        console.log('按 Ctrl+C 停止服务器');
        
        // 优雅关闭
        process.on('SIGINT', () => {
            console.log('\n🛑 正在关闭服务器...');
            server.close(() => {
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testHttpServer();
}

module.exports = {
    isPortAvailable,
    getAvailablePort,
    createHttpServer
};

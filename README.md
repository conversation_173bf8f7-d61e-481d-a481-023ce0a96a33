# local-plugin

一个VSCode插件，提供本地开发工具功能。

## 功能特性

### 🚀 本地HTTP服务器

快速启动一个本地HTTP服务器，用于开发和测试。

**特性：**
- 自动检测可用端口（避免端口冲突）
- 随机端口分配（3000-9999范围）
- 内置简单的API接口
- 支持CORS跨域请求
- 友好的Web界面
- 一键启动/停止/重启

**使用方法：**
1. 打开命令面板 (`Cmd+Shift+P` 或 `Ctrl+Shift+P`)
2. 输入 "启动本地HTTP服务器" 或 "Start HTTP Server"
3. 选择相应的操作（打开浏览器、复制地址、停止服务器）

**内置API接口：**
- `GET /` - 服务器主页
- `GET /api/status` - 服务器状态信息
- `GET /api/time` - 当前时间
- `POST /api/echo` - 回显请求数据

### 其他功能

- Hello World 示例命令
- Focus Augment 功能

## 安装要求

- Visual Studio Code 1.101.0 或更高版本
- Node.js 环境

## 使用说明

### 启动HTTP服务器

1. **通过命令面板：**
   - 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
   - 输入 "启动本地HTTP服务器"
   - 回车执行

2. **服务器启动后：**
   - 会自动找到可用端口（通常从3000开始）
   - 显示成功通知，包含端口信息
   - 可选择打开浏览器、复制地址或停止服务器

3. **管理服务器：**
   - 如果服务器已运行，再次执行命令可以选择重启、停止或打开浏览器
   - 服务器会一直运行直到手动停止或VSCode关闭

### API使用示例

```bash
# 获取服务器状态
curl http://localhost:3001/api/status

# 获取当前时间
curl http://localhost:3001/api/time

# 发送POST请求测试回显
curl -X POST http://localhost:3001/api/echo \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello World"}'
```

## Release Notes

Users appreciate release notes as you update your extension.

### 1.0.0

Initial release of ...

### 1.0.1

Fixed issue #.

### 1.1.0

Added features X, Y, and Z.

---

## Working with Markdown

You can author your README using Visual Studio Code.  Here are some useful editor keyboard shortcuts:

* Split the editor (`Cmd+\` on macOS or `Ctrl+\` on Windows and Linux)
* Toggle preview (`Shift+Cmd+V` on macOS or `Shift+Ctrl+V` on Windows and Linux)
* Press `Ctrl+Space` (Windows, Linux, macOS) to see a list of Markdown snippets

## For more information

* [Visual Studio Code's Markdown Support](http://code.visualstudio.com/docs/languages/markdown)
* [Markdown Syntax Reference](https://help.github.com/articles/markdown-basics/)

**Enjoy!**

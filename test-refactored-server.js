const http = require('http');
const net = require('net');
const { handleRequest } = require('./src/commands/local-plugin.startHttpServer/route');

/**
 * 检查端口是否被占用
 * @param {number} port 端口号
 * @returns {Promise<boolean>} 端口是否可用
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * 获取随机可用端口
 * @param {number} startPort 起始端口
 * @param {number} endPort 结束端口
 * @returns {Promise<number>} 可用端口号
 */
async function getAvailablePort(startPort = 3000, endPort = 9999) {
    for (let port = startPort; port <= endPort; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    
    // 如果指定范围内没有可用端口，尝试系统分配
    return new Promise((resolve, reject) => {
        const server = net.createServer();
        server.listen(0, () => {
            const port = server.address().port;
            server.close(() => resolve(port));
        });
        server.on('error', reject);
    });
}

/**
 * 创建HTTP服务器（使用重构后的路由）
 * @param {number} port 端口号
 * @returns {Promise<http.Server>} HTTP服务器实例
 */
function createHttpServer(port) {
    return new Promise((resolve, reject) => {
        const server = http.createServer((req, res) => {
            // 使用重构后的路由处理器
            handleRequest(req, res, port);
        });
        
        server.listen(port, () => {
            console.log(`HTTP服务器启动成功，端口: ${port}`);
            resolve(server);
        });
        
        server.on('error', (err) => {
            console.error('HTTP服务器启动失败:', err);
            reject(err);
        });
    });
}

// 测试函数
async function testHttpServer() {
    try {
        console.log('🔍 正在查找可用端口...');
        const port = await getAvailablePort();
        console.log(`✅ 找到可用端口: ${port}`);
        
        console.log('🚀 启动HTTP服务器（使用重构后的路由）...');
        const server = await createHttpServer(port);
        
        console.log(`🌐 服务器已启动: http://localhost:${port}`);
        console.log('📋 可用接口:');
        console.log('  - GET  /           - 服务器主页');
        console.log('  - GET  /api/status - 服务器状态');
        console.log('  - GET  /api/time   - 当前时间');
        console.log('  - POST /api/echo   - 回显请求');
        console.log('按 Ctrl+C 停止服务器');
        
        // 优雅关闭
        process.on('SIGINT', () => {
            console.log('\n🛑 正在关闭服务器...');
            server.close(() => {
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testHttpServer();
}

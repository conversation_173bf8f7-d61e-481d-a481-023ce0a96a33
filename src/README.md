# 项目结构说明

## 目录结构

```
src/
├── extension.js          # 扩展主入口文件
├── lib/                  # 公共库文件
│   └── commandRegistry.js # 命令注册器
└── commands/             # 命令实现目录
    └── local-plugin.helloWorld/  # Hello World 命令
        └── index.js      # 命令实现文件
```

## 如何添加新命令

1. **在 package.json 中声明命令**：
   ```json
   {
     "contributes": {
       "commands": [{
         "command": "local-plugin.newCommand",
         "title": "New Command"
       }]
     }
   }
   ```

2. **创建命令目录和实现文件**：
   ```
   src/commands/local-plugin.newCommand/
   └── index.js
   ```

3. **实现命令逻辑**：
   ```javascript
   const vscode = require('vscode');

   function execute() {
       // 命令实现逻辑
       vscode.window.showInformationMessage('New command executed!');
   }

   module.exports = {
       execute
   };
   ```

4. **重新启动扩展**：命令注册器会自动扫描并注册新命令。

## 设计原则

- **关注点分离**：命令逻辑与扩展入口分离
- **自动化注册**：无需手动注册每个命令
- **模块化**：每个命令独立成模块
- **可扩展性**：易于添加新功能和命令

## 调试

使用 VS Code 的调试功能：
1. 按 `F5` 启动调试
2. 在新窗口中测试命令
3. 在代码中设置断点进行调试

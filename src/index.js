const CommandRegistry = require('./lib/commandRegistry');

/**
 * 扩展激活函数
 * @param {vscode.ExtensionContext} context 
 */
function activate(context) {
    console.log('Congratulations, your extension "local-plugin" is now active!');

    // 创建命令注册器并注册所有命令
    const commandRegistry = new CommandRegistry();
    commandRegistry.registerAllCommands(context);
}

/**
 * 扩展停用函数
 */
function deactivate() {
    console.log('Extension "local-plugin" is being deactivated');
}

module.exports = {
    activate,
    deactivate
};

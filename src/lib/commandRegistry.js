const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

/**
 * 命令注册器
 * 自动扫描并注册 src/commands 目录下的所有命令
 */
class CommandRegistry {
    constructor() {
        this.commands = new Map();
    }

    /**
     * 注册所有命令
     * @param {vscode.ExtensionContext} context 
     */
    registerAllCommands(context) {
        const commandsDir = path.join(__dirname, '..', 'commands');
        
        try {
            // 扫描commands目录
            const commandFolders = fs.readdirSync(commandsDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            for (const commandName of commandFolders) {
                this.registerCommand(context, commandName);
            }

            console.log(`Successfully registered ${this.commands.size} commands`);
        } catch (error) {
            console.error('Error registering commands:', error);
        }
    }

    /**
     * 注册单个命令
     * @param {vscode.ExtensionContext} context 
     * @param {string} commandName 
     */
    registerCommand(context, commandName) {
        try {
            const commandPath = path.join(__dirname, '..', 'commands', commandName);
            const commandModule = require(commandPath);

            if (commandModule && typeof commandModule.execute === 'function') {
                const disposable = vscode.commands.registerCommand(commandName, commandModule.execute);
                context.subscriptions.push(disposable);
                this.commands.set(commandName, commandModule);
                
                console.log(`Registered command: ${commandName}`);
            } else {
                console.warn(`Command module ${commandName} does not export an execute function`);
            }
        } catch (error) {
            console.error(`Error registering command ${commandName}:`, error);
        }
    }

    /**
     * 获取已注册的命令列表
     */
    getRegisteredCommands() {
        return Array.from(this.commands.keys());
    }
}

module.exports = CommandRegistry;

# HTTP服务器命令

这个命令可以启动一个本地HTTP服务器，用于开发和测试。

## 代码结构

```
src/commands/local-plugin.startHttpServer/
├── index.js          # 主入口文件，包含VSCode命令逻辑和服务器启动逻辑
├── route.js          # 路由映射文件，只负责路由分发
├── handle/           # 业务逻辑处理器目录
│   ├── home.js       # 主页处理器
│   ├── status.js     # 状态API处理器
│   ├── time.js       # 时间API处理器
│   ├── echo.js       # 回显API处理器
│   └── notFound.js   # 404错误处理器
└── README.md         # 说明文档
```

## 设计原则

### 1. 职责分离
- **index.js**: 负责VSCode插件命令逻辑、端口检测、服务器创建
- **route.js**: 负责路由映射和分发，不包含具体业务逻辑
- **handle/*.js**: 每个文件负责一个特定路由的业务逻辑

### 2. 模块化
- 每个路由对应一个独立的处理器文件
- 便于维护和扩展新的API接口
- 代码结构清晰，易于理解

### 3. 可扩展性
- 添加新路由只需要：
  1. 在`handle/`目录创建新的处理器文件
  2. 在`route.js`的路由配置表中添加路由映射
  3. 无需修改其他文件

## 路由配置

路由配置在`route.js`中的`routes`数组中定义：

```javascript
const routes = [
    {
        method: 'GET',
        path: '/',
        handler: handleHome,
        description: '服务器主页'
    },
    // ... 其他路由
];
```

## 处理器规范

每个处理器文件应该导出一个处理函数，函数签名为：

```javascript
function handleXxx(req, res, port) {
    // 处理逻辑
    // req: HTTP请求对象
    // res: HTTP响应对象
    // port: 服务器端口号
}

module.exports = { handleXxx };
```

## 可用接口

### GET /
- **描述**: 服务器主页，显示服务器信息和可用接口
- **响应**: HTML页面

### GET /api/status
- **描述**: 获取服务器状态信息
- **响应**: JSON格式的状态数据

### GET /api/time
- **描述**: 获取当前时间信息
- **响应**: JSON格式的时间数据

### POST /api/echo
- **描述**: 回显请求数据
- **请求**: 任意数据
- **响应**: JSON格式的请求信息

## 使用方法

### 在VSCode中使用
1. 打开命令面板 (`Cmd+Shift+P` 或 `Ctrl+Shift+P`)
2. 输入 "启动本地HTTP服务器"
3. 选择相应操作

### 独立测试
```bash
# 测试重构后的服务器
node test-refactored-server.js
```

## 扩展示例

要添加一个新的API接口，例如`/api/hello`：

1. **创建处理器** `handle/hello.js`:
```javascript
function handleHello(req, res, port) {
    const data = {
        message: 'Hello, World!',
        timestamp: new Date().toISOString(),
        port: port
    };
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(data, null, 2));
}

module.exports = { handleHello };
```

2. **更新路由配置** `route.js`:
```javascript
const { handleHello } = require('./handle/hello');

const routes = [
    // ... 现有路由
    {
        method: 'GET',
        path: '/api/hello',
        handler: handleHello,
        description: '问候接口'
    }
];
```

就这么简单！新的接口就可以使用了。

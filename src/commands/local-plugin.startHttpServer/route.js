/**
 * HTTP服务器路由处理模块
 */

/**
 * 处理根路径请求
 * @param {number} port 服务器端口
 * @returns {string} HTML响应内容
 */
function handleHomePage(port) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>VSCode Local HTTP Server</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; margin: 0 auto; }
                .status { color: #28a745; font-weight: bold; }
                .info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                .api-list { list-style: none; padding: 0; }
                .api-list li { margin: 10px 0; padding: 10px; background: #fff; border: 1px solid #dee2e6; border-radius: 3px; }
                .api-list a { text-decoration: none; color: #007bff; font-weight: bold; }
                .api-list a:hover { text-decoration: underline; }
                .method {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    margin-right: 10px;
                }
                .get { background: #28a745; color: white; }
                .post { background: #ffc107; color: black; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 VSCode Local HTTP Server</h1>
                <p class="status">✅ 服务器运行中</p>
                <div class="info">
                    <h3>服务器信息</h3>
                    <p><strong>端口:</strong> ${port}</p>
                    <p><strong>地址:</strong> http://localhost:${port}</p>
                    <p><strong>启动时间:</strong> ${new Date().toLocaleString()}</p>
                </div>
                <div class="info">
                    <h3>可用接口</h3>
                    <ul class="api-list">
                        <li>
                            <span class="method get">GET</span>
                            <a href="/api/status">/api/status</a>
                            <span> - 服务器状态信息</span>
                        </li>
                        <li>
                            <span class="method get">GET</span>
                            <a href="/api/time">/api/time</a>
                            <span> - 当前时间</span>
                        </li>
                        <li>
                            <span class="method post">POST</span>
                            <span>/api/echo</span>
                            <span> - 回显请求数据</span>
                        </li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
    `;
}

/**
 * 处理API状态请求
 * @param {number} port 服务器端口
 * @returns {object} 状态信息对象
 */
function handleApiStatus(port) {
    return {
        status: 'running',
        port: port,
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        endpoints: [
            { method: 'GET', path: '/', description: '服务器主页' },
            { method: 'GET', path: '/api/status', description: '服务器状态' },
            { method: 'GET', path: '/api/time', description: '当前时间' },
            { method: 'POST', path: '/api/echo', description: '回显请求数据' }
        ]
    };
}

/**
 * 处理API时间请求
 * @returns {object} 时间信息对象
 */
function handleApiTime() {
    return {
        time: new Date().toISOString(),
        timestamp: Date.now(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: new Date().toLocaleString()
    };
}

/**
 * 处理API回显请求
 * @param {object} req HTTP请求对象
 * @param {string} body 请求体内容
 * @returns {object} 回显信息对象
 */
function handleApiEcho(req, body) {
    return {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: body,
        timestamp: new Date().toISOString(),
        contentType: req.headers['content-type'] || 'unknown',
        contentLength: body.length
    };
}

/**
 * 处理404错误
 * @param {string} url 请求的URL
 * @returns {object} 错误信息对象
 */
function handle404(url) {
    return {
        error: 'Not Found',
        message: `路径 ${url} 不存在`,
        timestamp: new Date().toISOString(),
        availableEndpoints: [
            'GET /',
            'GET /api/status',
            'GET /api/time',
            'POST /api/echo'
        ]
    };
}

/**
 * 主路由处理函数
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleRequest(req, res, port) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 路由分发
    if (req.url === '/') {
        // 主页
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(handleHomePage(port));

    } else if (req.url === '/api/status') {
        // API状态
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(handleApiStatus(port), null, 2));

    } else if (req.url === '/api/time') {
        // API时间
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(handleApiTime(), null, 2));

    } else if (req.url === '/api/echo' && req.method === 'POST') {
        // API回显
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(handleApiEcho(req, body), null, 2));
        });

    } else {
        // 404错误
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(handle404(req.url), null, 2));
    }
}

module.exports = {
    handleRequest,
    handleHomePage,
    handleApiStatus,
    handleApiTime,
    handleApiEcho,
    handle404
};
/**
 * HTTP服务器路由映射模块
 * 只负责路由分发，具体业务逻辑在handle目录中
 */

const { handleHome } = require('./handle/home');
const { handleStatus } = require('./handle/status');
const { handleTime } = require('./handle/time');
const { handleEcho } = require('./handle/echo');
const { handleNotFound } = require('./handle/notFound');

/**
 * 路由配置表
 */
const routes = [
    {
        method: 'GET',
        path: '/focus',
        handler: handleHome,
        description: '服务器主页'
    },
    {
        method: 'GET',
        path: '/api/status',
        handler: handleStatus,
        description: '服务器状态信息'
    },
    {
        method: 'GET',
        path: '/api/time',
        handler: handleTime,
        description: '当前时间'
    },
    {
        method: 'POST',
        path: '/api/echo',
        handler: handleEcho,
        description: '回显请求数据'
    }
];

/**
 * 设置CORS头
 * @param {object} res HTTP响应对象
 */
function setCorsHeaders(res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

/**
 * 匹配路由
 * @param {string} method HTTP方法
 * @param {string} path 请求路径
 * @returns {object|null} 匹配的路由或null
 */
function matchRoute(method, path) {
    return routes.find(route =>
        route.method === method && route.path === path
    );
}

/**
 * 获取所有路由信息
 * @returns {array} 路由信息数组
 */
function getRoutes() {
    return routes.map(route => ({
        method: route.method,
        path: route.path,
        description: route.description
    }));
}

/**
 * 主路由处理函数
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleRequest(req, res, port) {
    // 设置CORS头
    setCorsHeaders(res);

    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 匹配路由
    const route = matchRoute(req.method, req.url);

    if (route) {
        // 找到匹配的路由，调用对应的处理器
        route.handler(req, res, port);
    } else {
        // 没有匹配的路由，返回404
        handleNotFound(req, res, port);
    }
}

module.exports = {
    handleRequest,
    getRoutes,
    matchRoute,
    setCorsHeaders
};
const vscode = require('vscode');
const http = require('http');
const net = require('net');
const path = require('path');
const fs = require('fs');

// 存储服务器实例
let httpServer = null;
let currentPort = null;

/**
 * 检查端口是否被占用
 * @param {number} port 端口号
 * @returns {Promise<boolean>} 端口是否可用
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * 获取随机可用端口
 * @param {number} startPort 起始端口
 * @param {number} endPort 结束端口
 * @returns {Promise<number>} 可用端口号
 */
async function getAvailablePort(startPort = 3000, endPort = 9999) {
    for (let port = startPort; port <= endPort; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    
    // 如果指定范围内没有可用端口，尝试系统分配
    return new Promise((resolve, reject) => {
        const server = net.createServer();
        server.listen(0, () => {
            const port = server.address().port;
            server.close(() => resolve(port));
        });
        server.on('error', reject);
    });
}

/**
 * 创建简单的HTTP服务器
 * @param {number} port 端口号
 * @returns {Promise<http.Server>} HTTP服务器实例
 */
function createHttpServer(port) {
    return new Promise((resolve, reject) => {
        const server = http.createServer((req, res) => {
            // 设置CORS头
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            
            // 简单的路由处理
            if (req.url === '/') {
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>VSCode Local HTTP Server</title>
                        <meta charset="utf-8">
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; }
                            .container { max-width: 600px; margin: 0 auto; }
                            .status { color: #28a745; font-weight: bold; }
                            .info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🚀 VSCode Local HTTP Server</h1>
                            <p class="status">✅ 服务器运行中</p>
                            <div class="info">
                                <h3>服务器信息</h3>
                                <p><strong>端口:</strong> ${port}</p>
                                <p><strong>地址:</strong> http://localhost:${port}</p>
                                <p><strong>启动时间:</strong> ${new Date().toLocaleString()}</p>
                            </div>
                            <div class="info">
                                <h3>可用接口</h3>
                                <ul>
                                    <li><a href="/api/status">GET /api/status</a> - 服务器状态</li>
                                    <li><a href="/api/time">GET /api/time</a> - 当前时间</li>
                                    <li>POST /api/echo - 回显请求数据</li>
                                </ul>
                            </div>
                        </div>
                    </body>
                    </html>
                `);
            } else if (req.url === '/api/status') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    status: 'running',
                    port: port,
                    uptime: process.uptime(),
                    timestamp: new Date().toISOString()
                }));
            } else if (req.url === '/api/time') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    time: new Date().toISOString(),
                    timestamp: Date.now()
                }));
            } else if (req.url === '/api/echo' && req.method === 'POST') {
                let body = '';
                req.on('data', chunk => {
                    body += chunk.toString();
                });
                req.on('end', () => {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        method: req.method,
                        url: req.url,
                        headers: req.headers,
                        body: body,
                        timestamp: new Date().toISOString()
                    }));
                });
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    error: 'Not Found',
                    message: `路径 ${req.url} 不存在`,
                    timestamp: new Date().toISOString()
                }));
            }
        });
        
        server.listen(port, () => {
            console.log(`HTTP服务器启动成功，端口: ${port}`);
            resolve(server);
        });
        
        server.on('error', (err) => {
            console.error('HTTP服务器启动失败:', err);
            reject(err);
        });
    });
}

/**
 * 启动HTTP服务器命令的实现
 */
async function execute() {
    try {
        // 如果服务器已经在运行，询问是否重启
        if (httpServer && currentPort) {
            const action = await vscode.window.showInformationMessage(
                `HTTP服务器已在端口 ${currentPort} 运行`,
                '重启服务器',
                '停止服务器',
                '打开浏览器'
            );
            
            if (action === '重启服务器') {
                httpServer.close();
                httpServer = null;
                currentPort = null;
            } else if (action === '停止服务器') {
                httpServer.close();
                httpServer = null;
                currentPort = null;
                vscode.window.showInformationMessage('HTTP服务器已停止');
                return;
            } else if (action === '打开浏览器') {
                vscode.env.openExternal(vscode.Uri.parse(`http://localhost:${currentPort}`));
                return;
            } else {
                return;
            }
        }
        
        // 显示进度
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "启动HTTP服务器",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0, message: "检查可用端口..." });
            
            // 获取可用端口
            const port = await getAvailablePort();
            currentPort = port;
            
            progress.report({ increment: 50, message: `找到可用端口 ${port}，启动服务器...` });
            
            // 创建并启动服务器
            httpServer = await createHttpServer(port);
            
            progress.report({ increment: 100, message: "服务器启动成功!" });
        });
        
        // 显示成功消息并提供操作选项
        const action = await vscode.window.showInformationMessage(
            `🚀 HTTP服务器启动成功！端口: ${currentPort}`,
            '打开浏览器',
            '复制地址',
            '停止服务器'
        );
        
        if (action === '打开浏览器') {
            vscode.env.openExternal(vscode.Uri.parse(`http://localhost:${currentPort}`));
        } else if (action === '复制地址') {
            vscode.env.clipboard.writeText(`http://localhost:${currentPort}`);
            vscode.window.showInformationMessage('地址已复制到剪贴板');
        } else if (action === '停止服务器') {
            httpServer.close();
            httpServer = null;
            currentPort = null;
            vscode.window.showInformationMessage('HTTP服务器已停止');
        }
        
    } catch (error) {
        console.error('启动HTTP服务器失败:', error);
        vscode.window.showErrorMessage(`启动HTTP服务器失败: ${error.message}`);
    }
}

module.exports = { execute };

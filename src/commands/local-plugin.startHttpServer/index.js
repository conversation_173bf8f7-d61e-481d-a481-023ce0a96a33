const vscode = require('vscode');
const http = require('http');
const net = require('net');
const { handleRequest } = require('./route');

// 存储服务器实例
let httpServer = null;
let currentPort = null;

/**
 * 检查端口是否被占用
 * @param {number} port 端口号
 * @returns {Promise<boolean>} 端口是否可用
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

/**
 * 获取随机可用端口
 * @param {number} startPort 起始端口
 * @param {number} endPort 结束端口
 * @returns {Promise<number>} 可用端口号
 */
async function getAvailablePort(startPort = 3000, endPort = 9999) {
    for (let port = startPort; port <= endPort; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    
    // 如果指定范围内没有可用端口，尝试系统分配
    return new Promise((resolve, reject) => {
        const server = net.createServer();
        server.listen(0, () => {
            const port = server.address().port;
            server.close(() => resolve(port));
        });
        server.on('error', reject);
    });
}

/**
 * 创建简单的HTTP服务器
 * @param {number} port 端口号
 * @returns {Promise<http.Server>} HTTP服务器实例
 */
function createHttpServer(port) {
    return new Promise((resolve, reject) => {
        const server = http.createServer((req, res) => {
            // 使用路由模块处理请求
            handleRequest(req, res, port);
        });
        
        server.listen(port, () => {
            console.log(`HTTP服务器启动成功，端口: ${port}`);
            resolve(server);
        });
        
        server.on('error', (err) => {
            console.error('HTTP服务器启动失败:', err);
            reject(err);
        });
    });
}

/**
 * 启动HTTP服务器命令的实现
 */
async function execute() {
    try {
        // 如果服务器已经在运行，询问是否重启
        if (httpServer && currentPort) {
            const action = await vscode.window.showInformationMessage(
                `HTTP服务器已在端口 ${currentPort} 运行`,
                '重启服务器',
                '停止服务器',
                '打开浏览器'
            );
            
            if (action === '重启服务器') {
                httpServer.close();
                httpServer = null;
                currentPort = null;
            } else if (action === '停止服务器') {
                httpServer.close();
                httpServer = null;
                currentPort = null;
                vscode.window.showInformationMessage('HTTP服务器已停止');
                return;
            } else if (action === '打开浏览器') {
                vscode.env.openExternal(vscode.Uri.parse(`http://localhost:${currentPort}`));
                return;
            } else {
                return;
            }
        }
        
        // 显示进度
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "启动HTTP服务器",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0, message: "检查可用端口..." });
            
            // 获取可用端口
            const port = await getAvailablePort();
            currentPort = port;
            
            progress.report({ increment: 50, message: `找到可用端口 ${port}，启动服务器...` });
            
            // 创建并启动服务器
            httpServer = await createHttpServer(port);
            
            progress.report({ increment: 100, message: "服务器启动成功!" });
        });
        
        // 显示成功消息并提供操作选项
        const action = await vscode.window.showInformationMessage(
            `🚀 HTTP服务器启动成功！端口: ${currentPort}`,
            '打开浏览器',
            '复制地址',
            '停止服务器'
        );
        
        if (action === '打开浏览器') {
            vscode.env.openExternal(vscode.Uri.parse(`http://localhost:${currentPort}`));
        } else if (action === '复制地址') {
            vscode.env.clipboard.writeText(`http://localhost:${currentPort}`);
            vscode.window.showInformationMessage('地址已复制到剪贴板');
        } else if (action === '停止服务器') {
            httpServer.close();
            httpServer = null;
            currentPort = null;
            vscode.window.showInformationMessage('HTTP服务器已停止');
        }
        
    } catch (error) {
        console.error('启动HTTP服务器失败:', error);
        vscode.window.showErrorMessage(`启动HTTP服务器失败: ${error.message}`);
    }
}

module.exports = { execute };

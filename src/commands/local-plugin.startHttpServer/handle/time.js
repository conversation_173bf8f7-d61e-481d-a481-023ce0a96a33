/**
 * 时间API处理器
 */

/**
 * 处理时间API请求
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleTime(req, res, port) {
    const now = new Date();
    const timeData = {
        time: now.toISOString(),
        timestamp: now.getTime(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: now.toLocaleString(),
        formats: {
            iso: now.toISOString(),
            utc: now.toUTCString(),
            local: now.toLocaleString(),
            date: now.toDateString(),
            time: now.toTimeString()
        },
        components: {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            day: now.getDate(),
            hour: now.getHours(),
            minute: now.getMinutes(),
            second: now.getSeconds(),
            millisecond: now.getMilliseconds()
        }
    };
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(timeData, null, 2));
}

module.exports = { handleTime };

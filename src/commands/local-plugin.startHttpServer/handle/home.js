/**
 * 主页处理器
 */

/**
 * 处理主页请求
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleHome(req, res, port) {
    const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>VSCode Local HTTP Server</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; margin: 0 auto; }
                .status { color: #28a745; font-weight: bold; }
                .info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                .api-list { list-style: none; padding: 0; }
                .api-list li { margin: 10px 0; padding: 10px; background: #fff; border: 1px solid #dee2e6; border-radius: 3px; }
                .api-list a { text-decoration: none; color: #007bff; font-weight: bold; }
                .api-list a:hover { text-decoration: underline; }
                .method {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    margin-right: 10px;
                }
                .get { background: #28a745; color: white; }
                .post { background: #ffc107; color: black; }
                .footer { margin-top: 40px; text-align: center; color: #6c757d; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 VSCode Local HTTP Server</h1>
                <p class="status">✅ 服务器运行中</p>
                <div class="info">
                    <h3>📊 服务器信息</h3>
                    <p><strong>端口:</strong> ${port}</p>
                    <p><strong>地址:</strong> <a href="http://localhost:${port}" target="_blank">http://localhost:${port}</a></p>
                    <p><strong>启动时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>运行时长:</strong> ${Math.floor(process.uptime())}秒</p>
                </div>
                <div class="info">
                    <h3>🔗 可用接口</h3>
                    <ul class="api-list">
                        <li>
                            <span class="method get">GET</span>
                            <a href="/api/status">/api/status</a>
                            <span> - 服务器状态信息</span>
                        </li>
                        <li>
                            <span class="method get">GET</span>
                            <a href="/api/time">/api/time</a>
                            <span> - 当前时间</span>
                        </li>
                        <li>
                            <span class="method post">POST</span>
                            <span>/api/echo</span>
                            <span> - 回显请求数据</span>
                        </li>
                    </ul>
                </div>
                <div class="footer">
                    <p>Powered by VSCode Local Plugin</p>
                </div>
            </div>
        </body>
        </html>
    `;
    
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(html);
}

module.exports = { handleHome };

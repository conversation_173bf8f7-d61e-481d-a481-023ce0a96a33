/**
 * 回显API处理器
 */

/**
 * 处理回显API请求
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleEcho(req, res, port) {
    let body = '';
    
    req.on('data', chunk => {
        body += chunk.toString();
    });
    
    req.on('end', () => {
        const echoData = {
            request: {
                method: req.method,
                url: req.url,
                headers: req.headers,
                body: body,
                contentType: req.headers['content-type'] || 'unknown',
                contentLength: body.length
            },
            server: {
                port: port,
                timestamp: new Date().toISOString(),
                uptime: process.uptime()
            },
            parsed: {
                bodyAsJson: tryParseJson(body),
                queryParams: parseQueryParams(req.url),
                userAgent: req.headers['user-agent'] || 'unknown'
            }
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(echoData, null, 2));
    });
}

/**
 * 尝试解析JSON
 * @param {string} str 字符串
 * @returns {object|null} 解析结果或null
 */
function tryParseJson(str) {
    try {
        return JSON.parse(str);
    } catch (e) {
        return null;
    }
}

/**
 * 解析查询参数
 * @param {string} url URL字符串
 * @returns {object} 查询参数对象
 */
function parseQueryParams(url) {
    const queryIndex = url.indexOf('?');
    if (queryIndex === -1) return {};
    
    const queryString = url.substring(queryIndex + 1);
    const params = {};
    
    queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key) {
            params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
        }
    });
    
    return params;
}

module.exports = { handleEcho };

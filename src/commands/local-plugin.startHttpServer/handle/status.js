/**
 * 状态API处理器
 */

/**
 * 处理状态API请求
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleStatus(req, res, port) {
    const statusData = {
        status: 'running',
        port: port,
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        server: {
            platform: process.platform,
            nodeVersion: process.version,
            memory: process.memoryUsage(),
            pid: process.pid
        },
        endpoints: [
            { method: 'GET', path: '/', description: '服务器主页' },
            { method: 'GET', path: '/api/status', description: '服务器状态' },
            { method: 'GET', path: '/api/time', description: '当前时间' },
            { method: 'POST', path: '/api/echo', description: '回显请求数据' }
        ]
    };
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(statusData, null, 2));
}

module.exports = { handleStatus };

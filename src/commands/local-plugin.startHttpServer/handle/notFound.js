/**
 * 404错误处理器
 */

/**
 * 处理404错误
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleNotFound(req, res, port) {
    const errorData = {
        error: 'Not Found',
        message: `路径 ${req.url} 不存在`,
        timestamp: new Date().toISOString(),
        request: {
            method: req.method,
            url: req.url,
            headers: req.headers
        },
        server: {
            port: port,
            uptime: process.uptime()
        },
        availableEndpoints: [
            { method: 'GET', path: '/', description: '服务器主页' },
            { method: 'GET', path: '/api/status', description: '服务器状态' },
            { method: 'GET', path: '/api/time', description: '当前时间' },
            { method: 'POST', path: '/api/echo', description: '回显请求数据' }
        ],
        suggestions: getSuggestions(req.url)
    };
    
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(errorData, null, 2));
}

/**
 * 根据请求路径提供建议
 * @param {string} url 请求的URL
 * @returns {string[]} 建议列表
 */
function getSuggestions(url) {
    const suggestions = [];
    const availablePaths = ['/', '/api/status', '/api/time', '/api/echo'];
    
    // 简单的路径匹配建议
    if (url.includes('status')) {
        suggestions.push('尝试访问 /api/status');
    }
    if (url.includes('time')) {
        suggestions.push('尝试访问 /api/time');
    }
    if (url.includes('echo')) {
        suggestions.push('尝试POST请求到 /api/echo');
    }
    if (url === '/api' || url === '/api/') {
        suggestions.push('API接口需要具体路径，如 /api/status');
    }
    
    // 如果没有特定建议，提供通用建议
    if (suggestions.length === 0) {
        suggestions.push('访问根路径 / 查看所有可用接口');
    }
    
    return suggestions;
}

module.exports = { handleNotFound };

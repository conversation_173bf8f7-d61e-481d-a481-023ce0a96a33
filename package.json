{"name": "local-plugin", "displayName": "local-plugin", "description": "", "version": "0.0.1", "engines": {"vscode": "^1.101.0"}, "categories": ["Other"], "activationEvents": [], "main": "./extension.js", "contributes": {"commands": [{"command": "local-plugin.helloWorld", "title": "Hello World"}, {"command": "local-plugin.focusAugment", "title": "local-plugin: Focus Augment"}, {"command": "local-plugin.startHttpServer", "title": "启动本地HTTP服务器"}]}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.101.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "eslint": "^9.25.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}